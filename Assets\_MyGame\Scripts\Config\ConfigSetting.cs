using System.Collections.Generic;
using System.Reflection;

public class ConfigSetting : ConfigBase
{
    private static SettingWrapper _setting;
    public static SettingWrapper Setting
    {
        get
        {
            _setting ??= new SettingWrapper();
            return _setting;
        }
    }
    public class SettingWrapper
    {
        public int everyLevelIncreaseDifficulty;
        public int startHuaCount;
        public int addHuaCount;
        public int maxHuaCount;
        public int startMajiangCount;
        public int addMajiangCount;
        public int maxMajiangCount;
        public int guideItemCount;
        public int reliveCount;
        public int clearSlotBarNeedGold;
        public int addTimeNeedGold;
        public int addTimeSecond;
        public int pigAddGold;
        public int pigGetGold;
        public int pigMaxGold;
        public int freeHeartCount;
        public int maxComboCount;
        public string itemCanUseMaxStr;
        public float[] goldMults;

        public int GetItemCanUseMax(int level) => ConfigSetting.GetItemCanUseMax(level);
    }

    internal override void CacheData(object key, ConfigInfoBase info)
    {
        base.CacheData(key, info);
        var infoSetting = info as InfoSetting;
        var id = key.ToString();

        // 特殊属性的自定义处理
        switch (id)
        {
            case "itemCanUseMax":
                Setting.itemCanUseMaxStr = infoSetting.paramString;
                return;
            case "goldMult":
                var goldMult = infoSetting.paramString;
                var mults = goldMult.Split(',');
                Setting.goldMults = new float[mults.Length];
                for (int i = 0; i < mults.Length; i++)
                {
                    float.TryParse(mults[i], out Setting.goldMults[i]);
                }
                return;
        }

        // 通用属性的自动赋值
        var settingType = typeof(SettingWrapper);
        var field = settingType.GetField(id);
        if (field != null)
        {
            if (field.FieldType == typeof(int))
            {
                field.SetValue(Setting, infoSetting.paramInt);
            }
            else if (field.FieldType == typeof(string))
            {
                field.SetValue(Setting, infoSetting.paramString);
            }
        }
    }



    private static Dictionary<int, int> _canUseItemDic;
    public static int GetItemCanUseMax(int level)
    {
        if (_canUseItemDic == null)
        {
            _canUseItemDic = new Dictionary<int, int>();
            var itemAry = Setting.itemCanUseMaxStr.Split('|');
            for (int i = 0; i < itemAry.Length; i++)
            {
                var ary = itemAry[i].Split('-');
                if (ary.Length == 2 && int.TryParse(ary[0], out int key) && int.TryParse(ary[1], out int value))
                {
                    _canUseItemDic[key] = value;
                }
            }
        }

        var canUseItemCount = 4;
        foreach (var item in _canUseItemDic)
        {
            if (level <= item.Key)
            {
                canUseItemCount = item.Value;
                break;
            }
        }
        return canUseItemCount;
    }
}