using System.Collections.Generic;

public class ConfigSetting : ConfigBase
{
    private static SettingWrapper _setting;
    public static SettingWrapper Setting
    {
        get
        {
            _setting ??= new SettingWrapper();
            return _setting;
        }
    }
    public class SettingWrapper
    {
        public int everyLevelIncreaseDifficulty;
        public int startHuaCount;
        public int addHuaCount;
        public int maxHuaCount;
        public int startMajiangCount;
        public int addMajiangCount;
        public int maxMajiangCount;
        public int guideItemCount;
        public int reliveCount;
        public int clearSlotBarNeedGold;
        public int addTimeNeedGold;
        public int addTimeSecond;
        public int pigAddGold;
        public int pigGetGold;
        public int pigMaxGold;
        public int freeHeartCount;
        public int maxComboCount;
        public string itemCanUseMaxStr;
        public float[] goldMults;

        public int GetItemCanUseMax(int level) => ConfigSetting.GetItemCanUseMax(level);
    }

    internal override void CacheData(object key, ConfigInfoBase info)
    {
        base.CacheData(key, info);
        var infoSetting = info as InfoSetting;
        var id = key.ToString();
        switch (id)
        {
            case "everyLevelIncreaseDifficulty":
                Setting.everyLevelIncreaseDifficulty = infoSetting.paramInt;
                break;
            case "startHuaCount":
                Setting.startHuaCount = infoSetting.paramInt;
                break;
            case "addHuaCount":
                Setting.addHuaCount = infoSetting.paramInt;
                break;
            case "maxHuaCount":
                Setting.maxHuaCount = infoSetting.paramInt;
                break;
            case "startMajiangCount":
                Setting.startMajiangCount = infoSetting.paramInt;
                break;
            case "addMajiangCount":
                Setting.addMajiangCount = infoSetting.paramInt;
                break;
            case "maxMajiangCount":
                Setting.maxMajiangCount = infoSetting.paramInt;
                break;
            case "guideItemCount":
                Setting.guideItemCount = infoSetting.paramInt;
                break;
            case "reliveCount":
                Setting.reliveCount = infoSetting.paramInt;
                break;
            case "clearSlotBarNeedGold":
                Setting.clearSlotBarNeedGold = infoSetting.paramInt;
                break;
            case "addTimeNeedGold":
                Setting.addTimeNeedGold = infoSetting.paramInt;
                break;
            case "addTimeSecond":
                Setting.addTimeSecond = infoSetting.paramInt;
                break;
            case "pigAddGold":
                Setting.pigAddGold = infoSetting.paramInt;
                break;
            case "pigGetGold":
                Setting.pigGetGold = infoSetting.paramInt;
                break;
            case "pigMaxGold":
                Setting.pigMaxGold = infoSetting.paramInt;
                break;
            case "freeHeartCount":
                Setting.freeHeartCount = infoSetting.paramInt;
                break;
            case "maxComboCount":
                Setting.maxComboCount = infoSetting.paramInt;
                break;
            case "itemCanUseMax":
                Setting.itemCanUseMaxStr = infoSetting.paramString;
                break;
            case "goldMult":
                var goldMult = infoSetting.paramString;
                var mults = goldMult.Split(',');
                Setting.goldMults = new float[mults.Length];
                for (int i = 0; i < mults.Length; i++)
                {
                    float.TryParse(mults[i], out Setting.goldMults[i]);
                }
                break;
        }
    }



    private static Dictionary<int, int> _canUseItemDic;
    public static int GetItemCanUseMax(int level)
    {
        if (_canUseItemDic == null)
        {
            _canUseItemDic = new Dictionary<int, int>();
            var itemAry = ItemCanUseMaxStr.Split('|');
            for (int i = 0; i < itemAry.Length; i++)
            {
                var ary = itemAry[i].Split('-');
                if (ary.Length == 2 && int.TryParse(ary[0], out int key) && int.TryParse(ary[1], out int value))
                {
                    _canUseItemDic[key] = value;
                }
            }
        }

        var canUseItemCount = 4;
        foreach (var item in _canUseItemDic)
        {
            if (level <= item.Key)
            {
                canUseItemCount = item.Value;
                break;
            }
        }
        return canUseItemCount;
    }
}