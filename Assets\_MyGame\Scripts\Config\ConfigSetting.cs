using System.Collections.Generic;

public class ConfigSetting : ConfigBase
{
    private static SettingWrapper _setting;
    public static SettingWrapper Setting
    {
        get
        {
            _setting ??= new SettingWrapper();
            return _setting;
        }
    }
    public class SettingWrapper
    {
        /// <summary>每过几关提升难度</summary>
        public int everyLevelIncreaseDifficulty;
        /// <summary>第一关多少花色</summary>
        public int startHuaCount;
        /// <summary>每过n关加多少种花色</summary>
        public int addHuaCount;
        /// <summary>花色上限</summary>
        public int maxHuaCount;
        /// <summary>第一关多少麻将</summary>
        public int startMajiangCount;
        /// <summary>每过n关加多少麻将(需要3的倍数)</summary>
        public int addMajiangCount;
        /// <summary>麻将数量上限</summary>
        public int maxMajiangCount;
        /// <summary>体验道具数量</summary>
        public int guideItemCount;
        /// <summary>可复活次数</summary>
        public int reliveCount;
        /// <summary>清空槽位所需金币</summary>
        public int clearSlotBarNeedGold;
        /// <summary>加时所需金币</summary>
        public int addTimeNeedGold;
        /// <summary>加时(秒)</summary>
        public int addTimeSecond;
        /// <summary>通1关加多少金币到金猪</summary>
        public int pigAddGold;
        /// <summary>存到多少可领取</summary>
        public int pigGetGold;
        /// <summary>金猪存储上限</summary>
        public int pigMaxGold;
        /// <summary>免费体力每次加多少个</summary>
        public int freeHeartCount;
        /// <summary>最大连击数量</summary>
        public int maxComboCount;
        /// <summary>每个道具使用上限(10-2表示第10关(包含)内上限都是2个)</summary>
        public string itemCanUseMaxStr;
        /// <summary>胜利结算翻倍倍率条(7个)</summary>
        public float[] goldMults;

        public int GetItemCanUseMax(int level) => ConfigSetting.GetItemCanUseMax(level);
    }

    internal override void CacheData(object key, ConfigInfoBase info)
    {
        var infoSetting = info as InfoSetting;
        var id = key.ToString();

        switch (id)
        {
            case "everyLevelIncreaseDifficulty": // 每过几关提升难度
                Setting.everyLevelIncreaseDifficulty = infoSetting.paramInt;
                break;
            case "startHuaCount": // 第一关多少花色
                Setting.startHuaCount = infoSetting.paramInt;
                break;
            case "addHuaCount": // 每过n关加多少种花色
                Setting.addHuaCount = infoSetting.paramInt;
                break;
            case "maxHuaCount": // 花色上限
                Setting.maxHuaCount = infoSetting.paramInt;
                break;
            case "startMajiangCount": // 第一关多少麻将
                Setting.startMajiangCount = infoSetting.paramInt;
                break;
            case "addMajiangCount": // 每过n关加多少麻将(需要3的倍数)
                Setting.addMajiangCount = infoSetting.paramInt;
                break;
            case "maxMajiangCount": // 麻将数量上限
                Setting.maxMajiangCount = infoSetting.paramInt;
                break;
            case "guideItemCount": // 体验道具数量
                Setting.guideItemCount = infoSetting.paramInt;
                break;
            case "reliveCount": // 可复活次数
                Setting.reliveCount = infoSetting.paramInt;
                break;
            case "clearSlotBarNeedGold": // 清空槽位所需金币
                Setting.clearSlotBarNeedGold = infoSetting.paramInt;
                break;
            case "addTimeNeedGold": // 加时所需金币
                Setting.addTimeNeedGold = infoSetting.paramInt;
                break;
            case "addTimeSecond": // 加时(秒)
                Setting.addTimeSecond = infoSetting.paramInt;
                break;
            case "pigAddGold": // 通1关加多少金币到金猪
                Setting.pigAddGold = infoSetting.paramInt;
                break;
            case "pigGetGold": // 存到多少可领取
                Setting.pigGetGold = infoSetting.paramInt;
                break;
            case "pigMaxGold": // 金猪存储上限
                Setting.pigMaxGold = infoSetting.paramInt;
                break;
            case "freeHeartCount": // 免费体力每次加多少个
                Setting.freeHeartCount = infoSetting.paramInt;
                break;
            case "maxComboCount": // 最大连击数量
                Setting.maxComboCount = infoSetting.paramInt;
                break;
            case "itemCanUseMax": // 每个道具使用上限
                Setting.itemCanUseMaxStr = infoSetting.paramString;
                break;
            case "goldMult": // 胜利结算翻倍倍率条(7个)
                var goldMult = infoSetting.paramString;
                var mults = goldMult.Split(',');
                Setting.goldMults = new float[mults.Length];
                for (int i = 0; i < mults.Length; i++)
                {
                    float.TryParse(mults[i], out Setting.goldMults[i]);
                }
                break;
        }
    }



    private static Dictionary<int, int> _canUseItemDic;
    public static int GetItemCanUseMax(int level)
    {
        if (_canUseItemDic == null)
        {
            _canUseItemDic = new Dictionary<int, int>();
            var itemAry = Setting.itemCanUseMaxStr.Split('|');
            for (int i = 0; i < itemAry.Length; i++)
            {
                var ary = itemAry[i].Split('-');
                if (ary.Length == 2 && int.TryParse(ary[0], out int key) && int.TryParse(ary[1], out int value))
                {
                    _canUseItemDic[key] = value;
                }
            }
        }

        var canUseItemCount = 4;
        foreach (var item in _canUseItemDic)
        {
            if (level <= item.Key)
            {
                canUseItemCount = item.Value;
                break;
            }
        }
        return canUseItemCount;
    }
}