public class ConfigSetting : ConfigBase
{
    private static InfoSetting _setting;
    public static InfoSetting Setting
    {
        get
        {
            if (_setting == null)
            {
                _setting = new InfoSetting();
                var config = ConfigManager.GetConfig<ConfigSetting>();
                var settingList = config.GetList<InfoSetting>();

                // 合并所有配置项到一个InfoSetting对象中
                foreach (var item in settingList)
                {
                    _setting.everyLevelIncreaseDifficulty = item.everyLevelIncreaseDifficulty != 0 ? item.everyLevelIncreaseDifficulty : _setting.everyLevelIncreaseDifficulty;
                    _setting.startHuaCount = item.startHuaCount != 0 ? item.startHuaCount : _setting.startHuaCount;
                    _setting.addHuaCount = item.addHuaCount != 0 ? item.addHuaCount : _setting.addHuaCount;
                    _setting.maxHuaCount = item.maxHuaCount != 0 ? item.maxHuaCount : _setting.maxHuaCount;
                    _setting.startMajiangCount = item.startMajiangCount != 0 ? item.startMajiangCount : _setting.startMajiangCount;
                    _setting.addMajiangCount = item.addMajiangCount != 0 ? item.addMajiangCount : _setting.addMajiangCount;
                    _setting.maxMajiangCount = item.maxMajiangCount != 0 ? item.maxMajiangCount : _setting.maxMajiangCount;
                    _setting.guideItemCount = item.guideItemCount != 0 ? item.guideItemCount : _setting.guideItemCount;
                    _setting.reliveCount = item.reliveCount != 0 ? item.reliveCount : _setting.reliveCount;
                    _setting.clearSlotBarNeedGold = item.clearSlotBarNeedGold != 0 ? item.clearSlotBarNeedGold : _setting.clearSlotBarNeedGold;
                    _setting.addTimeNeedGold = item.addTimeNeedGold != 0 ? item.addTimeNeedGold : _setting.addTimeNeedGold;
                    _setting.addTimeSecond = item.addTimeSecond != 0 ? item.addTimeSecond : _setting.addTimeSecond;
                    _setting.goldMults = item.goldMults != null ? item.goldMults : _setting.goldMults;
                    _setting.pigAddGold = item.pigAddGold != 0 ? item.pigAddGold : _setting.pigAddGold;
                    _setting.pigGetGold = item.pigGetGold != 0 ? item.pigGetGold : _setting.pigGetGold;
                    _setting.pigMaxGold = item.pigMaxGold != 0 ? item.pigMaxGold : _setting.pigMaxGold;
                    _setting.freeHeartCount = item.freeHeartCount != 0 ? item.freeHeartCount : _setting.freeHeartCount;
                    _setting.itemCanUseMaxStr = !string.IsNullOrEmpty(item.itemCanUseMaxStr) ? item.itemCanUseMaxStr : _setting.itemCanUseMaxStr;
                    _setting.maxComboCount = item.maxComboCount != 0 ? item.maxComboCount : _setting.maxComboCount;
                }
            }
            return _setting;
        }
    }
}