using System.Collections.Generic;

public class ConfigSetting : ConfigBase
{
    public static int EveryLevelIncreaseDifficulty => GetIntValue("everyLevelIncreaseDifficulty");
    public static int StartHuaCount => GetIntValue("startHuaCount");
    public static int AddHuaCount => GetIntValue("addHuaCount");
    public static int MaxHuaCount => GetIntValue("maxHuaCount");
    public static int StartMajiangCount => GetIntValue("startMajiangCount");
    public static int AddMajiangCount => GetIntValue("addMajiangCount");
    public static int MaxMajiangCount => GetIntValue("maxMajiangCount");
    public static int GuideItemCount => GetIntValue("guideItemCount");
    public static int ReliveCount => GetIntValue("reliveCount");
    public static int ClearSlotBarNeedGold => GetIntValue("clearSlotBarNeedGold");
    public static int AddTimeNeedGold => GetIntValue("addTimeNeedGold");
    public static int AddTimeSecond => GetIntValue("addTimeSecond");
    public static int PigAddGold => GetIntValue("pigAddGold");
    public static int PigGetGold => GetIntValue("pigGetGold");
    public static int PigMaxGold => GetIntValue("pigMaxGold");
    public static int FreeHeartCount => GetIntValue("freeHeartCount");
    public static int MaxComboCount => GetIntValue("maxComboCount");

    public static string ItemCanUseMaxStr => GetStringValue("itemCanUseMax");
    public static float[] GoldMults => GetFloatArrayValue("goldMult");

    // 为了向后兼容，提供Setting属性
    private static SettingWrapper _setting;
    public static SettingWrapper Setting
    {
        get
        {
            _setting ??= new SettingWrapper();
            return _setting;
        }
    }

    public class SettingWrapper
    {
        public int everyLevelIncreaseDifficulty;
        public int startHuaCount => StartHuaCount;
        public int addHuaCount => AddHuaCount;
        public int maxHuaCount => MaxHuaCount;
        public int startMajiangCount => StartMajiangCount;
        public int addMajiangCount => AddMajiangCount;
        public int maxMajiangCount => MaxMajiangCount;
        public int guideItemCount => GuideItemCount;
        public int reliveCount => ReliveCount;
        public int clearSlotBarNeedGold => ClearSlotBarNeedGold;
        public int addTimeNeedGold => AddTimeNeedGold;
        public int addTimeSecond => AddTimeSecond;
        public int pigAddGold => PigAddGold;
        public int pigGetGold => PigGetGold;
        public int pigMaxGold => PigMaxGold;
        public int freeHeartCount => FreeHeartCount;
        public int maxComboCount => MaxComboCount;
        public string itemCanUseMaxStr => ItemCanUseMaxStr;
        public float[] goldMults => GoldMults;

        public int GetItemCanUseMax(int level) => ConfigSetting.GetItemCanUseMax(level);
    }

    internal override void CacheData(object key, ConfigInfoBase info)
    {
        var infoSetting = info as InfoSetting;
        var id = key.ToString();
        switch (id)
        {
            case "everyLevelIncreaseDifficulty":
                Setting.everyLevelIncreaseDifficulty = GetIntValue("everyLevelIncreaseDifficulty");
                break;
        }
    }

    private static int GetIntValue(string id)
    {
        var config = ConfigManager.GetConfig<ConfigSetting>();
        var info = config.GetData<InfoSetting>(id);
        return info?.paramInt ?? 0;
    }

    private static string GetStringValue(string id)
    {
        var config = ConfigManager.GetConfig<ConfigSetting>();
        var info = config.GetData<InfoSetting>(id);
        return info?.paramString ?? "";
    }

    private static float[] GetFloatArrayValue(string id)
    {
        var config = ConfigManager.GetConfig<ConfigSetting>();
        var info = config.GetData<InfoSetting>(id);
        if (info?.paramString == null) return new float[0];

        var parts = info.paramString.Split(',');
        var result = new float[parts.Length];
        for (int i = 0; i < parts.Length; i++)
        {
            float.TryParse(parts[i], out result[i]);
        }
        return result;
    }

    private static Dictionary<int, int> _canUseItemDic;
    public static int GetItemCanUseMax(int level)
    {
        if (_canUseItemDic == null)
        {
            _canUseItemDic = new Dictionary<int, int>();
            var itemAry = ItemCanUseMaxStr.Split('|');
            for (int i = 0; i < itemAry.Length; i++)
            {
                var ary = itemAry[i].Split('-');
                if (ary.Length == 2 && int.TryParse(ary[0], out int key) && int.TryParse(ary[1], out int value))
                {
                    _canUseItemDic[key] = value;
                }
            }
        }

        var canUseItemCount = 4;
        foreach (var item in _canUseItemDic)
        {
            if (level <= item.Key)
            {
                canUseItemCount = item.Value;
                break;
            }
        }
        return canUseItemCount;
    }
}