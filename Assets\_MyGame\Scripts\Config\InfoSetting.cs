using System;
using System.Collections.Generic;
using LitJson;
[Serializable]
public class InfoSetting : ConfigInfoBase
{
    public int everyLevelIncreaseDifficulty;
    public int startHuaCount;
    public int addHuaCount;
    public int maxHuaCount;
    public int startMajiangCount;
    public int addMajiangCount;
    public int maxMajiangCount;
    public int guideItemCount;
    public int reliveCount;
    public int clearSlotBarNeedGold;
    public int addTimeNeedGold;
    public int addTimeSecond;
    public float[] goldMults;
    public int pigAddGold;
    public int pigGetGold;
    public int pigMaxGold;
    public int freeHeartCount;
    public string itemCanUseMaxStr;
    public int maxComboCount;
    public override object GetKey(int index)
    {
        return index;
    }

    public override void Parse(JsonData json)
    {
        everyLevelIncreaseDifficulty = JsonUtil.ToInt(json, "everyLevelIncreaseDifficulty");
        startHuaCount = JsonUtil.ToInt(json, "startHuaCount");
        addHuaCount = JsonUtil.ToInt(json, "addHuaCount");
        maxHuaCount = JsonUtil.ToInt(json, "maxHuaCount");
        startMajiangCount = JsonUtil.ToInt(json, "startMajiangCount");
        addMajiangCount = JsonUtil.ToInt(json, "addMajiangCount");
        maxMajiangCount = JsonUtil.ToInt(json, "maxMajiangCount");
        guideItemCount = JsonUtil.ToInt(json, "guideItemCount");
        reliveCount = JsonUtil.ToInt(json, "reliveCount");
        clearSlotBarNeedGold = JsonUtil.ToInt(json, "clearSlotBarNeedGold");
        addTimeNeedGold = JsonUtil.ToInt(json, "addTimeNeedGold");
        addTimeSecond = JsonUtil.ToInt(json, "addTimeSecond");
        var goldMult = JsonUtil.ToString(json, "goldMult");
        var mults = goldMult.Split(',');
        goldMults = new float[mults.Length];
        for (int i = 0; i < mults.Length; i++)
        {
            goldMults[i] = float.Parse(mults[i]);
        }
        pigAddGold = JsonUtil.ToInt(json, "pigAddGold");
        pigGetGold = JsonUtil.ToInt(json, "pigGetGold");
        pigMaxGold = JsonUtil.ToInt(json, "pigMaxGold");
        freeHeartCount = JsonUtil.ToInt(json, "freeHeartCount");

        itemCanUseMaxStr = JsonUtil.ToString(json, "itemCanUseMax");
        maxComboCount = JsonUtil.ToInt(json, "maxComboCount");
    }

    private Dictionary<int, int> canUseItemDic;
    public int GetItemCanUseMax(int level)
    {
        if (canUseItemDic == null)
        {
            canUseItemDic = new Dictionary<int, int>();
            var itemAry = itemCanUseMaxStr.Split('|');
            for (int i = 0; i < itemAry.Length; i++)
            {
                var ary = itemAry[i].Split('-');
                canUseItemDic[int.Parse(ary[0])] = int.Parse(ary[1]);
            }
        }

        var canUseItemCount = 4;
        foreach (var item in canUseItemDic)
        {
            if (level <= item.Key)
            {
                canUseItemCount = item.Value;
                break;
            }
        }
        return canUseItemCount;
    }
}